import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/models/user.dart' as app;
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/login/service/login_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:rolio/modules/user/service/user_service.dart';
import 'package:rolio/common/utils/error_handler.dart';

/// 用户控制器
class UserController extends GetxController {
  /// 登录服务
  final LoginService _loginService;
  
  /// 用户头像服务
  final UserAvatarService _avatarService;
  
  /// 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();
  
  /// 当前用户
  final Rx<app.User?> user = Rx<app.User?>(null);
  
  /// 是否已登录
  final RxBool isLoggedIn = false.obs;
  
  /// 是否是游客
  final RxBool isGuest = true.obs;
  
  /// 是否正在更新用户信息
  final RxBool isUpdating = false.obs;
  
  // 可观察的头像列表
  final RxList<String> avatarUrls = <String>[].obs;
  
  // 可观察的当前选择的头像
  final RxString selectedAvatar = ''.obs;
  
  // 用户名验证常量
  static const int MIN_USERNAME_LENGTH = 1;
  static const int MAX_USERNAME_LENGTH = 30;
  static const String USERNAME_PATTERN = r'^[a-zA-Z0-9_\-\. ]+$';
  
  /// 构造函数
  UserController({
    required LoginService loginService,
    required UserAvatarService avatarService,
  }) : 
    _loginService = loginService,
    _avatarService = avatarService;
  
  @override
  void onInit() {
    super.onInit();
    _initUserState();
    // 监听全局用户状态变化
    ever(_globalState.currentUser, _updateUserState);
    // 加载头像列表
    _loadAvatarUrls();
  }
  
  /// 加载头像URL列表
  void _loadAvatarUrls() {
    avatarUrls.value = _avatarService.getAvatarUrls();
    
    // 如果没有选择头像，默认选择当前用户头像或第一个
    if (user.value?.photoURL != null && user.value!.photoURL!.isNotEmpty) {
      selectedAvatar.value = user.value!.photoURL!;
    } else if (avatarUrls.isNotEmpty) {
      selectedAvatar.value = avatarUrls.first;
    }
  }
  
  /// 选择头像
  void selectAvatar(String avatarUrl) {
    if (avatarUrls.contains(avatarUrl)) {
      selectedAvatar.value = avatarUrl;
      LogUtil.debug('已选择头像: $avatarUrl');
    }
  }
  
  /// 更新用户头像
  Future<bool> updateAvatar() async {
    try {
      isUpdating.value = true;
      
      // 验证选择的头像
      if (selectedAvatar.isEmpty) {
        ErrorHandler.showInfo('Please select an avatar');
        return false;
      }
      
      // 如果选择的头像与当前头像相同，不需要更新
      if (user.value?.photoURL == selectedAvatar.value) {
        LogUtil.debug('选择的头像与当前头像相同，跳过更新');
        return true;
      }
      
      // 调用服务更新头像
      final success = await _avatarService.updatePhotoURL(selectedAvatar.value);
      
      if (success) {
        // 刷新用户信息
        await refreshUserInfo();
        
        // 显示成功提示
        ErrorHandler.showSuccess('Avatar updated successfully');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('更新头像失败: $e');
      ErrorHandler.showInfo('Failed to update avatar');
      return false;
    } finally {
      isUpdating.value = false;
    }
  }
  
  /// 初始化用户状态
  Future<void> _initUserState() async {
    try {
      // 从服务获取当前用户
      final currentUser = await _loginService.getCurrentUser();
      LogUtil.debug('_initUserState获取到用户: ${currentUser?.uid}, 是否匿名: ${currentUser?.isAnonymous}');
      _updateUserState(currentUser);
      
      // 如果用户存在且有头像，初始化选中的头像
      if (currentUser != null && currentUser.photoURL != null && currentUser.photoURL!.isNotEmpty) {
        selectedAvatar.value = currentUser.photoURL!;
      }
    } catch (e) {
      LogUtil.error('初始化用户状态失败: $e');
    }
  }
  
  /// 更新用户状态
  void _updateUserState(app.User? currentUser) {
    user.value = currentUser;
    // 用户存在且不是匿名用户（游客）时才算登录
    isLoggedIn.value = currentUser != null;
    isGuest.value = currentUser?.isAnonymous ?? true;
    
    LogUtil.debug('_updateUserState完成 - user: ${currentUser?.uid}, isLoggedIn: ${isLoggedIn.value}, isGuest: ${isGuest.value}');
  }
  
  /// 退出登录
  /// 返回true表示用户确认并完成登出，false表示用户取消登出
  Future<bool> logout() async {
    // 显示确认对话框
    final bool? confirmed = await Get.dialog<bool>(
      AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'Confirm Logout',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Are you sure you want to log out?',
          style: TextStyle(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text(
              'Logout',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    // 如果用户确认登出
    if (confirmed == true) {
      try {
        LogUtil.info('开始登出流程...');
        
        // 1. 登出用户
        await _loginService.logoutUser();
        LogUtil.debug('用户已登出，开始匿名登录...');
        
        // 2. 等待匿名登录完成
        final anonymousUser = await _loginService.signInAnonymously();
        
        // 3. 确保匿名登录成功并更新用户状态
        if (anonymousUser.user != null) {
          LogUtil.info('匿名登录成功，uid: ${anonymousUser.user!.uid}');
          
          // 重要：更新UserController的用户状态
          await _initUserState();
          
          // 添加调试日志确认状态更新
          LogUtil.debug('UserController状态更新后 - isLoggedIn: ${isLoggedIn.value}, isGuest: ${isGuest.value}');
          
          ErrorHandler.showSuccess('Logged out');
          return true; // 登出成功
        } else {
          LogUtil.error('匿名登录失败: 未返回用户');
          ErrorHandler.showInfo('Logged out, but anonymous login failed');
          return true; // 虽然匿名登录失败，但用户已登出
        }
      } catch (e) {
        LogUtil.error('退出登录失败: $e');
        ErrorHandler.showInfo('Failed to log out');
        return false; // 登出失败
      }
    }
    
    // 用户取消登出
    return false;
  }
  
  /// 刷新用户信息
  Future<void> refreshUserInfo() async {
    await _initUserState();
  }
  
  /// 更新用户名
  Future<bool> updateUsername(String newName) async {
    try {
      // 检查是否为匿名用户
      if (isGuest.value) {
        ErrorHandler.showInfo('Guest users cannot change username');
        return false;
      }
      
      // 验证用户名是否为空
      if (newName.trim().isEmpty) {
        ErrorHandler.showInfo('Username cannot be empty');
        return false;
      }
      
      // 验证用户名长度
      if (newName.length < MIN_USERNAME_LENGTH) {
        ErrorHandler.showInfo('Username must be at least $MIN_USERNAME_LENGTH characters');
        return false;
      }
      
      if (newName.length > MAX_USERNAME_LENGTH) {
        ErrorHandler.showInfo('Username cannot exceed $MAX_USERNAME_LENGTH characters');
        return false;
      }
      
      // 验证用户名格式
      final RegExp usernameRegex = RegExp(USERNAME_PATTERN);
      if (!usernameRegex.hasMatch(newName)) {
        ErrorHandler.showInfo('Username can only contain letters, numbers, underscore, dot, hyphen and space');
        return false;
      }
      
      isUpdating.value = true;
      
      // 调用服务更新用户名
      final success = await _avatarService.updateDisplayName(newName);
      
      if (success) {
        // 刷新用户信息
        await refreshUserInfo();
        
      }
      
      return success;
    } catch (e) {
      LogUtil.error('更新用户名失败: $e');
      ErrorHandler.showInfo('Failed to update username');
      return false;
    } finally {
      isUpdating.value = false;
    }
  }
  
  /// 为了兼容现有代码，保留旧方法名，内部调用新方法
  Future<void> updateUserName(String newName) async {
    await updateUsername(newName);
  }
  
  /// 为了兼容现有代码，保留旧方法名，内部调用新方法
  Future<void> updateUserPhoto(String photoURL) async {
    selectedAvatar.value = photoURL;
    await updateAvatar();
  }
}