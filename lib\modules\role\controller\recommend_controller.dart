import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/common/event/event_bus.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/models/page_request.dart';
import 'package:rolio/common/services/session_binding_service.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/modules/role/service/recommend_service.dart';
import 'package:rolio/routes/router_manager.dart';
import 'package:rolio/routes/routes.dart';
import 'package:rolio/modules/chat/service/chat_manager.dart';
import 'package:rolio/common/services/role_provider.dart';

/// 推荐控制器
/// 负责管理推荐页面的UI状态和业务逻辑
class RecommendController extends GetxController {
  // 服务和管理器 - 通过GetX获取
  late final RecommendService recommendService;
  IChatService? chatService;
  late final GlobalState globalState;
  
  // 加载状态 - 代理服务层状态
  RxBool get isLoading => recommendService.isLoadingRoles;
  
  // 是否显示骨架屏 - 控制UI显示
  final RxBool showSkeleton = true.obs;
  
  // 图片预加载器
  final ImagePreloader _imagePreloader = ImagePreloader();
  
  // 取消令牌标识符
  final String _cancelTokenId = 'recommend_controller';
  
  // 分页相关
  final int pageSize = StringsConsts.recommendPageSize;
  PageRequest _currentRequest = PageRequest(page: 1, size: StringsConsts.recommendPageSize);
  
  // 分页状态
  final RxBool isLoadingMore = false.obs;
  final RxBool hasMoreData = true.obs;
  
  // 最后一次刷新时间戳，防止频繁刷新
  int _lastRefreshTime = 0;
  static const int MIN_REFRESH_INTERVAL = StringsConsts.recommendRefreshInterval;
  
  // 首次加载标志
  bool _isFirstLoad = true;
  
  // 状态持久化 - 保存当前页码和总数据量
  static int _savedCurrentPage = 1;
  static int _savedTotalItems = 0;
  static bool _hasSavedState = false;
  
  // 事件订阅
  StreamSubscription? _refreshDataSubscription;
  
  @override
  void onInit() {
    super.onInit();
    
    // 通过GetX获取依赖
    recommendService = Get.find<RecommendService>();
    globalState = Get.find<GlobalState>();
    
    // 尝试获取ChatService，如果不可用不阻止初始化
    try {
      if (Get.isRegistered<IChatService>()) {
        chatService = Get.find<IChatService>();
        LogUtil.debug('RecommendController: 已获取IChatService');
      } else {
        LogUtil.warn('RecommendController: IChatService未注册，聊天功能可能不可用');
      }
    } catch (e) {
      LogUtil.warn('RecommendController: 获取IChatService失败: $e');
    }
    
    // 监听WebSocket连接状态
    _setupWebSocketListener();
    
    // 监听登录后的推荐数据刷新事件
    _setupRefreshDataListener();
    
    // 恢复之前的分页状态
    _restorePaginationState();
    
    // 不再在初始化时自动加载数据
    // 改为等待登录完成获取token后再加载
    // _loadRecommendedRoles(forceRefresh: !_hasSavedState);
    
    // 监听推荐服务的角色列表变化
    ever(recommendService.recommendedRoles, (roles) {
      LogUtil.debug('推荐角色列表已更新，共${recommendService.recommendedRoles.length}个角色');
      
      // 保存当前状态，以便页面切换后恢复
      _saveCurrentState();
      
      // 数据加载完成后，预加载图片
      if (roles.isNotEmpty) {
        _preloadRoleImages(roles);
      }
      
      // 数据加载完成后，无论列表是否为空都隐藏骨架屏，显示空状态UI
      if (showSkeleton.value) {
        Future.delayed(const Duration(milliseconds: 300), () {
          showSkeleton.value = false;
        });
      }
      
      update(); // 通知UI更新
    });
    
    // 监听加载状态变化
    ever(isLoading, (bool loading) {
      // 只有在非首次加载、非加载更多的情况下，才显示全页骨架屏
      if (loading && !_isFirstLoad && !isLoadingMore.value) {
        LogUtil.debug('显示全页骨架屏 (非首次加载且非加载更多)');
        showSkeleton.value = true;
      }
      
      // 如果加载完成，标记非首次加载
      if (!loading) {
        _isFirstLoad = false;
      }
    });
  }
  
  /// 初始化数据加载
  /// 
  /// 在登录完成后调用此方法，确保有token后再加载数据
  void initializeData() {
    // 检查是否有token
    if (globalState.accessToken.isEmpty) {
      LogUtil.warn('初始化推荐数据失败：无可用token');
      return;
    }
    
    LogUtil.info('初始化推荐数据 (token已获取)');
    _loadRecommendedRoles(forceRefresh: !_hasSavedState);
  }
  
  /// 设置刷新数据监听器
  void _setupRefreshDataListener() {
    _refreshDataSubscription = EventBus().on('refresh_recommend_data').listen((event) {
      LogUtil.info('收到刷新推荐数据事件: $event');
      
      // 获取事件数据
      final data = event['data'];
      if (data is Map) {
        final forceRefresh = data['forceRefresh'] as bool? ?? true;
        final source = data['source'] as String? ?? 'unknown';
        
        LogUtil.info('从[$source]触发刷新推荐数据，强制刷新: $forceRefresh');
        
        // 执行刷新
        loadRecommendedRoles(forceRefresh: forceRefresh);
      } else {
        // 默认执行刷新
        loadRecommendedRoles(forceRefresh: true);
      }
    });
  }
  
  // 设置WebSocket连接状态监听
  void _setupWebSocketListener() {
    try {
      final wsManager = Get.find<WsManager>();
      
      // 使用RxState来跟踪连接状态，避免重复触发
      final RxInt lastState = 0.obs;
      
      // 订阅连接状态变化
      wsManager.connectionStateStream.listen((state) {
        // 如果是从失败状态到已连接状态的变化，触发刷新
        if (lastState.value == WsConnectionState.failed.index && 
            state == WsConnectionState.connected) {
          LogUtil.info('检测到WebSocket重连成功，触发数据刷新');
          
          // 在微任务中执行，避免阻塞UI
          Future.microtask(() => refreshAfterReconnect());
        }
        
        // 更新上一次的状态
        lastState.value = state.index;
      });
      
      LogUtil.debug('已设置WebSocket连接状态监听');
    } catch (e) {
      LogUtil.error('设置WebSocket状态监听失败: $e');
    }
  }
  
  /// 保存当前分页状态
  void _saveCurrentState() {
    _savedCurrentPage = _currentRequest.page;
    _savedTotalItems = recommendService.recommendedRoles.length;
    _hasSavedState = true;
    
    // 更新是否有更多数据的状态
    hasMoreData.value = recommendService.hasMoreData;
    
    LogUtil.debug('保存推荐页状态: 当前页=${_savedCurrentPage}, 总数据量=${_savedTotalItems}, 是否有更多=${hasMoreData.value}');
  }
  
  /// 恢复之前保存的分页状态
  void _restorePaginationState() {
    if (_hasSavedState) {
      LogUtil.debug('恢复之前的分页状态: 页码=${_savedCurrentPage}, 总数据量=${_savedTotalItems}');
      _currentRequest = _currentRequest.copyWith(page: _savedCurrentPage);
      
      // 从服务层获取是否有更多数据的状态
      hasMoreData.value = recommendService.hasMoreData;
      
      LogUtil.debug('恢复后的请求页码: ${_currentRequest.page}, 是否有更多数据: ${hasMoreData.value}');
    } else {
      LogUtil.debug('没有保存的分页状态，使用初始值');
    }
  }
  
  /// 预加载角色图片
  void _preloadRoleImages(List<AiRole> roles) {
    // 提取所有需要预加载的图片URL
    final List<String> imageUrls = [];
    
    for (final role in roles) {
      if (role.avatarUrl.isNotEmpty) {
        imageUrls.add(role.avatarUrl);
      }
      if (role.coverUrl.isNotEmpty) {
        imageUrls.add(role.coverUrl);
      }
    }
    
    // 如果有图片需要预加载，开始预加载
    if (imageUrls.isNotEmpty) {
      LogUtil.debug('开始预加载${imageUrls.length}张角色图片');
      _imagePreloader.preloadImages(imageUrls);
    }
  }
  
  /// 获取角色列表 - 供UI层使用
  List<AiRole> getRoles() {
    return recommendService.recommendedRoles.toList();
  }
  
  /// 加载推荐角色 - 内部方法，处理业务逻辑
  Future<void> _loadRecommendedRoles({bool forceRefresh = false}) async {
    // 如果正在加载，不重复加载
    if (isLoading.value) {
      LogUtil.debug('正在加载中，跳过加载');
      return;
    }
    
    // 如果已经有角色数据且不强制刷新，不重复加载
    if (!forceRefresh && recommendService.recommendedRoles.isNotEmpty) {
      LogUtil.debug('已有角色数据且不强制刷新，跳过加载');
      
      // 即使不加载，也要确保hasMoreData状态正确
      hasMoreData.value = recommendService.hasMoreData;
      
      return;
    }
    
    try {
      // 调用服务层方法
      final success = await recommendService.getPagedRoles(_currentRequest, forceRefresh: forceRefresh);
      
      // 更新最后刷新时间
      _lastRefreshTime = DateTime.now().millisecondsSinceEpoch;
      
      // 更新是否有更多数据的状态
      hasMoreData.value = success && recommendService.hasMoreData;
      
      // 如果加载失败且没有现有数据，显示错误信息
      if (!success && recommendService.recommendedRoles.isEmpty) {
        // 使用统一的异常处理方法
        ErrorHandler.handleException(
          AppException(
            'failed to load recommended roles',
            code: ErrorCodes.ROLE_LIST_LOAD_FAILED
          )
        );
      }
    } catch (e) {
      LogUtil.error('加载推荐角色失败: $e');
      // 只有在没有现有数据时才显示错误，避免干扰用户体验
      if (recommendService.recommendedRoles.isEmpty) {
        // 使用handleException，让系统自动处理错误代码和消息
        ErrorHandler.handleException(e);
      }
    } finally {
      // 无论是否有数据，都隐藏骨架屏，显示相应的UI状态
      Future.delayed(const Duration(milliseconds: 300), () {
        showSkeleton.value = false;
      });
    }
  }
  
  /// 加载推荐角色 - 公开方法，供UI调用
  Future<void> loadRecommendedRoles({bool forceRefresh = false}) async {
    return _loadRecommendedRoles(forceRefresh: forceRefresh);
  }
  
  /// 加载更多角色 - 滚动到底部时调用
  Future<void> loadMoreRoles() async {
    // 分页边界检查
    if (isLoadingMore.value) {
      LogUtil.debug('已有加载任务正在进行中，跳过重复请求');
      return;
    }
    
    if (!hasMoreData.value) {
      LogUtil.debug('没有更多数据了，跳过加载');
      return;
    }
    
    try {
      isLoadingMore.value = true;
      
      // 创建下一页请求
      _currentRequest = _currentRequest.nextPage();
      LogUtil.info('加载更多角色：请求第${_currentRequest.page}页，当前列表大小: ${recommendService.recommendedRoles.length}');
      
      // 保存当前角色列表的长度，用于后续判断是否有新数据添加
      final previousLength = recommendService.recommendedRoles.length;
      
      // 调用服务层方法加载更多
      final success = await recommendService.getPagedRoles(_currentRequest, isLoadMore: true);
      
      // 检查是否有新数据添加
      final newItemsCount = recommendService.recommendedRoles.length - previousLength;
      
      if (newItemsCount > 0) {
        LogUtil.info('成功加载了$newItemsCount个新角色，当前总数: ${recommendService.recommendedRoles.length}');
        
        // 只有在有新数据添加的情况下，才根据服务层的状态更新hasMoreData
        hasMoreData.value = success && recommendService.hasMoreData;
      } else if (success) {
        LogUtil.warn('请求成功但未获取到新数据，可能所有数据都是重复的或服务器返回空列表');
        // 如果没有新数据，则认为没有更多数据了
        hasMoreData.value = false;
        LogUtil.debug('没有新数据，设置hasMoreData=false');
      } else {
        // 请求失败，保持之前的hasMoreData状态
        LogUtil.debug('请求失败，保持hasMoreData状态不变');
      }
      
      // 记录一些额外信息帮助排查
      LogUtil.debug('总数据量: ${recommendService.totalCount}, 已加载: ${recommendService.recommendedRoles.length}, hasMoreData: ${hasMoreData.value}');
      
      // 保存当前状态，以便页面切换后恢复
      _saveCurrentState();
      
    } catch (e) {
      LogUtil.error('加载更多角色失败: $e');
      // 回滚请求
      _currentRequest = _currentRequest.previousPage();
      // 统一使用ErrorHandler.handleException()方法
      ErrorHandler.handleException(e, showSnackbar: true);
    } finally {
      // 延迟关闭加载状态，确保UI动画平滑
      Future.delayed(const Duration(milliseconds: 300), () {
        isLoadingMore.value = false;
      });
    }
  }
  
  /// 刷新数据 - 用户下拉刷新时调用
  Future<void> refreshRecommendedRoles() async {
    // 检查距离上次刷新是否超过最小间隔
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now - _lastRefreshTime < MIN_REFRESH_INTERVAL) {
      LogUtil.debug('刷新间隔过短，跳过刷新');
      return;
    }
    
    // 重置分页参数和保存的状态
    _currentRequest = _currentRequest.resetPage();
    _hasSavedState = false;
    _savedCurrentPage = 1;
    _savedTotalItems = 0;
    
    // 重置加载更多状态
    hasMoreData.value = true;
    
    return _loadRecommendedRoles(forceRefresh: true);
  }
  
  /// 强制刷新数据 - 跳过刷新间隔限制
  Future<void> forceRefreshRecommendedRoles() async {
    LogUtil.debug('强制刷新推荐列表，跳过间隔检查');
    // 重置分页参数和保存的状态
    _currentRequest = _currentRequest.resetPage();
    _hasSavedState = false;
    _savedCurrentPage = 1;
    _savedTotalItems = 0;
    
    // 重置加载更多状态
    hasMoreData.value = true;
    
    return _loadRecommendedRoles(forceRefresh: true);
  }
  
  /// 处理滚动通知，用于自动加载更多
  bool handleScrollNotification(ScrollNotification notification) {
    // 只处理滚动结束通知，避免频繁触发
    if (notification is ScrollEndNotification) {
      // 如果滚动位置接近底部，且有更多数据可加载，且当前没有正在加载的任务
      if (notification.metrics.extentAfter < StringsConsts.loadMoreThreshold &&
          hasMoreData.value && 
          !isLoadingMore.value) {
        
        // 使用微任务延迟加载，避免在滚动动画过程中触发加载
        Future.microtask(() => loadMoreRoles());
      }
    }
    
    // 返回false允许滚动通知继续传递
    return false;
  }

  /// 开始与角色聊天
  Future<void> startChatWithRole(AiRole role) async {
    try {
      LogUtil.info('开始与角色聊天: ${role.name}, ID=${role.id}');
      
      // 检查角色是否有有效的会话ID
      if (role.conversationId != null && role.conversationId! > 0) {
        // 会话ID可能已过期，直接进入聊天页面，让ChatController处理会话检查
        LogUtil.debug('角色已有会话ID: ${role.conversationId}，直接进入聊天');
        _navigateToChatScreen(role, role.conversationId!);
        return;
      }
      
      // 如果没有会话ID，进入新会话模式
      LogUtil.debug('角色没有有效会话ID，进入新会话模式，显示greeting: ${role.greeting}');
      
      // 构建传递给聊天页面的参数
      final chatArguments = {
        StringsConsts.userId: 'ai_${role.id}',
        StringsConsts.username: role.name,
        StringsConsts.profilePic: role.avatarUrl,
        StringsConsts.isGroupChat: false,
        'aiRoleId': role.id,
        'coverUrl': role.coverUrl,
        'avatarUrl': role.avatarUrl,
        'greeting': role.greeting, // 传递问候语
        'description': role.description, // 传递角色描述
        'startNewChat': true, // 标记需要创建新会话
        'isFromRecommendList': true, // 标记是从推荐列表进入聊天
      };
      
      LogUtil.debug('传递给聊天页面的参数: $chatArguments');
        
      // 直接导航到聊天页面，让聊天页面自己处理聊天启动逻辑
      RouterManager.navigateTo(
        Routes.chatScreen, 
        arguments: chatArguments
      );
      
      LogUtil.debug('已导航到聊天页面，角色ID: ${role.id}');
    } catch (e) {
      LogUtil.error('进入聊天失败: $e');
      ErrorHandler.handleException(e);
    }
  }
  
  /// 清除角色绑定关系
  void _clearRoleBinding(int roleId) {
    try {
      if (Get.isRegistered<SessionBindingService>()) {
        final bindingService = Get.find<SessionBindingService>();
        bindingService.clearBinding(roleId);
        LogUtil.debug('已清除角色绑定: roleId=$roleId');
        
        // 同时更新角色对象的conversationId
        _updateRoleConversationId(roleId);
      }
    } catch (e) {
      LogUtil.error('清除角色绑定失败: $e');
    }
  }
  
  /// 更新角色的会话ID
  Future<void> _updateRoleConversationId(int roleId) async {
    try {
      // 先获取角色对象
      final role = await recommendService.getRoleById(roleId);
      if (role != null) {
        // 创建更新后的角色，将conversationId设为null
        final updatedRole = role.copyWith(conversationId: null);
        // 调用服务更新角色
        await recommendService.updateRoleConversationId(roleId, 0, updatedRole);
        LogUtil.debug('已更新角色会话ID为null: roleId=$roleId');
      }
    } catch (e) {
      LogUtil.error('更新角色会话ID失败: $e');
    }
  }
  
  /// 导航到聊天页面
  void _navigateToChatScreen(AiRole role, int conversationId) {
    try {
      // 设置当前选中的AI角色 - 转换为通用AiRole类型
      globalState.setCurrentAiRole(role);
      
      // 使用RouterManager.navigateTo替代Get.toNamed，确保ChatBinding被正确执行
      RouterManager.navigateTo(
        Routes.chatScreen, 
        arguments: {
          StringsConsts.userId: 'ai_${role.id}',
          StringsConsts.username: role.name,
          StringsConsts.profilePic: role.avatarUrl,
          StringsConsts.isGroupChat: false,
          'conversationId': conversationId,
          'aiRoleId': role.id,
          'coverUrl': role.coverUrl,
          'avatarUrl': role.avatarUrl, // 额外添加avatarUrl字段，确保ChatController可以访问
          'isFromRecommendList': true, // 标记是从推荐列表进入聊天
          'description': role.description, // 添加角色描述
        }
      );
      
      LogUtil.debug('导航到聊天页面，会话ID: $conversationId, 角色ID: ${role.id}');
    } catch (e) {
      LogUtil.error('导航到聊天页面失败: $e');
      ErrorHandler.handleException(e, 
        message: '无法打开聊天页面，请重试'
      );
    }
  }
  
  /// 根据角色ID获取角色头像URL
  Future<String> getAvatarUrl(int roleId) async {
    try {
      // 使用服务层统一的方法获取头像URL
      final avatarUrl = await recommendService.getAvatarUrlById(roleId);
      return avatarUrl ?? StringsConsts.recommendDefaultAvatarUrl;
    } catch (e) {
      LogUtil.error('获取角色头像失败，角色ID: $roleId, 错误: $e');
      return StringsConsts.recommendDefaultAvatarUrl;
    }
  }
  
  /// 检查图片是否已预加载
  bool isImagePreloaded(String url) {
    return _imagePreloader.isImagePreloaded(url);
  }
  
  /// 获取预加载状态信息
  String getPreloadStatus() {
    return '已预加载: ${_imagePreloader.preloadedCount}张, 加载中: ${_imagePreloader.loadingCount}张, 队列中: ${_imagePreloader.queueCount}张';
  }
  
  /// 网络重连后刷新数据
  Future<void> refreshAfterReconnect() async {
    try {
      LogUtil.info('网络重连后刷新角色列表');
      
      // 重置分页参数
      _currentRequest = _currentRequest.resetPage();
      _hasSavedState = false;
      _savedCurrentPage = 1;
      _savedTotalItems = 0;
      
      // 重置加载更多状态
      hasMoreData.value = true;
      
      // 获取最新角色列表
      await forceRefreshRecommendedRoles();
      
      // 获取最新会话列表
      try {
        if (Get.isRegistered<ISessionProvider>()) {
          final sessionProvider = Get.find<ISessionProvider>();
          await sessionProvider.refreshSessions();
          LogUtil.debug('已刷新会话列表');
        }
      } catch (e) {
        LogUtil.error('刷新会话列表失败: $e');
      }
      
      // 同步会话状态
      if (chatService != null) {
        final chatManager = Get.find<ChatManager>();
        await chatManager.syncConversationState();
        LogUtil.debug('已同步会话状态');
      }
      
      LogUtil.info('重连刷新完成');
    } catch (e) {
      LogUtil.error('重连刷新失败: $e');
      ErrorHandler.handleException(e);
    }
  }

  /// 检查并恢复页面状态
  void checkAndRestoreState() {
    LogUtil.debug('检查并恢复推荐页状态');
    
    // 如果有保存的状态，确保数据和状态正确
    if (_hasSavedState) {
      // 确保hasMoreData状态正确
      hasMoreData.value = recommendService.hasMoreData;
      
      // 如果当前数据量小于保存的数据量，可能是因为缓存丢失，需要重新加载
      if (recommendService.recommendedRoles.length < _savedTotalItems) {
        LogUtil.debug('检测到数据丢失，当前数据量(${recommendService.recommendedRoles.length}) < 保存的数据量($_savedTotalItems)，重新加载');
        
        // 重置分页状态
        _currentRequest = _currentRequest.resetPage();
        _hasSavedState = false;
        hasMoreData.value = true;
        
        // 强制刷新数据
        _loadRecommendedRoles(forceRefresh: true);
      } else {
        LogUtil.debug('数据状态正常，无需重新加载');
      }
    }
  }
  
  @override
  void onClose() {
    // 取消所有网络请求
    HttpManager.cancelRequests(_cancelTokenId);
    LogUtil.debug('RecommendController 已关闭，取消了所有网络请求');
    
    // 取消事件订阅
    if (_refreshDataSubscription != null) {
      _refreshDataSubscription?.cancel();
      LogUtil.debug('RecommendController: 已取消数据刷新事件订阅');
    }
    
    // 确保释放所有资源
    recommendService.recommendedRoles.close();
    
    super.onClose();
  }
}