import 'package:rolio/common/constants/cache_constants.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/utils/logger.dart';

/// 缓存配置管理器
/// 
/// 统一管理所有模块的缓存策略和配置
class CacheConfigManager {
  static final CacheConfigManager _instance = CacheConfigManager._internal();
  factory CacheConfigManager() => _instance;
  CacheConfigManager._internal();

  /// 获取单例实例
  static CacheConfigManager get instance => _instance;

  // 缓存命中率统计
  final Map<String, int> _hitCount = {};
  final Map<String, int> _missCount = {};
  final Map<String, int> _totalRequests = {};

  /// 模块缓存配置
  static const Map<String, CacheModuleConfig> _moduleConfigs = {
    'role': CacheModuleConfig(
      listExpiry: CacheConstants.roleListExpiryMs,
      detailExpiry: CacheConstants.roleDetailExpiryMs,
      favoriteExpiry: CacheConstants.roleFavoriteExpiryMs,
      keyPrefix: 'role_',
      dependencies: ['recommend', 'search'],
    ),
    'recommend': CacheModuleConfig(
      listExpiry: CacheConstants.roleListExpiryMs,
      detailExpiry: CacheConstants.roleDetailExpiryMs,
      keyPrefix: 'recommend_',
      dependencies: ['role'],
    ),
    'search': CacheModuleConfig(
      listExpiry: CacheConstants.shortExpiryMs,
      detailExpiry: CacheConstants.roleDetailExpiryMs,
      keyPrefix: 'search_',
      dependencies: ['role'],
    ),
    'session': CacheModuleConfig(
      listExpiry: CacheConstants.sessionListExpiryMs,
      detailExpiry: CacheConstants.sessionDetailExpiryMs,
      keyPrefix: 'session_',
      dependencies: [],
    ),
    'chat': CacheModuleConfig(
      listExpiry: CacheConstants.chatHistoryExpiryMs,
      detailExpiry: CacheConstants.chatPendingExpiryMs,
      keyPrefix: 'chat_',
      dependencies: ['session'],
    ),
  };

  /// 获取模块缓存配置
  CacheModuleConfig getModuleConfig(String module) {
    return _moduleConfigs[module] ?? const CacheModuleConfig(
      listExpiry: CacheConstants.mediumExpiryMs,
      detailExpiry: CacheConstants.mediumExpiryMs,
      keyPrefix: 'default_',
      dependencies: [],
    );
  }

  /// 获取缓存键
  String getCacheKey(String module, String type, {Map<String, dynamic>? params, String? id}) {
    final config = getModuleConfig(module);
    final baseKey = '${config.keyPrefix}${type}';
    
    if (id != null) {
      return '${baseKey}_$id';
    }
    
    if (params != null && params.isNotEmpty) {
      final sortedKeys = params.keys.toList()..sort();
      final paramString = sortedKeys.map((key) => '$key=${params[key]}').join('_');
      return '${baseKey}_$paramString';
    }
    
    return baseKey;
  }

  /// 获取缓存过期时间
  int getCacheExpiry(String module, String type) {
    final config = getModuleConfig(module);
    switch (type) {
      case 'list':
        return config.listExpiry;
      case 'detail':
        return config.detailExpiry;
      case 'favorite':
        return config.favoriteExpiry ?? config.detailExpiry;
      default:
        return config.listExpiry;
    }
  }

  /// 获取缓存策略
  CacheStrategy getCacheStrategy({bool isRead = true}) {
    return isRead 
        ? CacheConstants.defaultGetStrategy 
        : CacheConstants.defaultCacheStrategy;
  }

  /// 记录缓存命中
  void recordCacheHit(String module, String type) {
    final key = '${module}_$type';
    _hitCount[key] = (_hitCount[key] ?? 0) + 1;
    _totalRequests[key] = (_totalRequests[key] ?? 0) + 1;
  }

  /// 记录缓存未命中
  void recordCacheMiss(String module, String type) {
    final key = '${module}_$type';
    _missCount[key] = (_missCount[key] ?? 0) + 1;
    _totalRequests[key] = (_totalRequests[key] ?? 0) + 1;
  }

  /// 获取缓存命中率
  double getCacheHitRate(String module, String type) {
    final key = '${module}_$type';
    final total = _totalRequests[key] ?? 0;
    if (total == 0) return 0.0;
    
    final hits = _hitCount[key] ?? 0;
    return hits / total;
  }

  /// 获取所有模块的缓存统计
  Map<String, Map<String, dynamic>> getCacheStatistics() {
    final stats = <String, Map<String, dynamic>>{};
    
    for (final entry in _totalRequests.entries) {
      final key = entry.key;
      final total = entry.value;
      final hits = _hitCount[key] ?? 0;
      final misses = _missCount[key] ?? 0;
      final hitRate = total > 0 ? hits / total : 0.0;
      
      stats[key] = {
        'total_requests': total,
        'hits': hits,
        'misses': misses,
        'hit_rate': hitRate,
      };
    }
    
    return stats;
  }

  /// 清理依赖缓存
  Future<void> invalidateDependentCaches(String module) async {
    final config = getModuleConfig(module);
    final cacheManager = CacheManager.to;
    
    // 清理当前模块缓存
    await _clearModuleCache(module);
    
    // 清理依赖模块缓存
    for (final dependency in config.dependencies) {
      await _clearModuleCache(dependency);
      LogUtil.debug('已清理依赖模块缓存: $dependency');
    }
    
    LogUtil.debug('已清理模块 $module 及其依赖的缓存');
  }

  /// 清理指定模块的缓存
  Future<void> _clearModuleCache(String module) async {
    final config = getModuleConfig(module);
    final cacheManager = CacheManager.to;
    
    // 清理内存缓存中以该模块前缀开头的所有键
    await cacheManager.clearByPrefix(config.keyPrefix);
  }

  /// 打印缓存统计信息
  void printCacheStatistics() {
    final stats = getCacheStatistics();
    LogUtil.info('=== 缓存统计信息 ===');
    
    for (final entry in stats.entries) {
      final key = entry.key;
      final data = entry.value;
      LogUtil.info('$key: 总请求=${data['total_requests']}, '
          '命中=${data['hits']}, 未命中=${data['misses']}, '
          '命中率=${(data['hit_rate'] * 100).toStringAsFixed(1)}%');
    }
  }

  /// 重置统计信息
  void resetStatistics() {
    _hitCount.clear();
    _missCount.clear();
    _totalRequests.clear();
    LogUtil.debug('缓存统计信息已重置');
  }
}

/// 缓存模块配置
class CacheModuleConfig {
  final int listExpiry;
  final int detailExpiry;
  final int? favoriteExpiry;
  final String keyPrefix;
  final List<String> dependencies;

  const CacheModuleConfig({
    required this.listExpiry,
    required this.detailExpiry,
    this.favoriteExpiry,
    required this.keyPrefix,
    required this.dependencies,
  });
}
